import logging
import time
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets
from rest_framework_simplejwt.authentication import JWTAuthentication
# from rest_framework.permissions import IsAdminUser
from setting.utils import refresh_image_sas_tokens
from user.models import UserSchedule
from user.permissions import IsSupervisorOrHigher
from django.conf import settings as django_settings
import subprocess

from .models import MessageTemplate, SystemSettings, PendingSettingChange
from .serializers import BusinessHoursSerializer, MessageTemplateSerializer, UserScheduleSerializer, ScheduleDataSerializer
from .services import SchedulingService, SettingsService
from devproject.utils.azure_storage import AzureBlobStorage
from devproject.utils.utils import bcolors
from datetime import datetime, timedelta
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from rest_framework.permissions import IsAuthenticated

from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger('django.api_logs')

# class SettingsView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     # def get(self, request):
#     #     key = request.query_params.get('key')
        
#     #     if key:
#     #         value = SettingsService.get_setting(key)
#     #         if value is None:
#     #             return Response({"error": f"Setting {key} not found"}, status=404)
#     #         return Response({key: value})
        
#     #     settings = SystemSettings.objects.all()
#     #     result = {}
#     #     for setting in settings:
#     #         result[setting.key] = setting.value if not setting.is_sensitive else "****"
        
#     #     return Response(result)

#     def get(self, request):      
#         # Check for a list of keys in request body
#         keys = request.data.get('keys', [])
        
#         # If a list of keys is provided in the request body
#         if keys:
#             if not isinstance(keys, list):
#                 return Response({"error": "The 'keys' parameter must be a list"}, status=400)
                
#             result = {}
#             not_found = []
            
#             for key in keys:
#                 setting = SystemSettings.objects.filter(key=key).first()
#                 if setting:
#                     # result[key] = setting.value if not setting.is_sensitive else "****"
#                     # Validate SAS token 
#                     if setting.value_type == 'image':
#                         updated_key, updated_value = refresh_image_sas_tokens(setting)
#                         if updated_value:
#                             result[key] = updated_value
#                             SettingsService.update_setting(updated_key, updated_value, request.user)
#                         else:
#                             result[key] = setting.value if not setting.is_sensitive else "****"
#                     else:
#                         result[key] = setting.value if not setting.is_sensitive else "****"
#                 else:
#                     not_found.append(key)
            
#             response_data = {"settings": result}
#             if not_found:
#                 response_data["not_found"] = not_found
                
#             return Response(response_data)
#             # return Response([response_data])
        
#         # If no keys specified, return all settings
#         else:
#             settings = SystemSettings.objects.all()
#             result = {}
#             for setting in settings:
#                 # Validate SAS token
#                 if setting.value_type == 'image':
#                     print(f"SettingsView's GET method's Validate SAS token of {setting.key} key with {setting.value}")
#                     updated_key, updated_value = refresh_image_sas_tokens(setting)
#                     if updated_value:
#                         result[setting.key] = updated_value
#                         SettingsService.update_setting(updated_key, updated_value, request.user)
#                     else:
#                         result[setting.key] = setting.value if not setting.is_sensitive else "****"
#                 else:
#                     result[setting.key] = setting.value if not setting.is_sensitive else "****"
            
#             return Response(result)
#             # return Response([result])
    
#     def post(self, request):
#         key = request.data.get('key')
#         value = request.data.get('value')
        
#         if not key or value is None:
#             return Response({"error": "Both key and value are required"}, status=400)
        
#         result = SettingsService.update_setting(key, value, request.user)
#         return Response(result)
    
#     # def put(self, request):
#     #     key = request.data.get('key')
#     #     value = request.data.get('value')
        
#     #     if not key:
#     #         return Response({"error": "Setting key is required"}, status=400)
        
#     #     try:
#     #         setting = SystemSettings.objects.get(key=key)
            
#     #         # Update the value if provided
#     #         if value is not None:
#     #             setting.value = value
#     #             setting.updated_by = request.user
#     #             setting.save()
                
#     #             # If setting requires restart, create a pending change record
#     #             if setting.requires_restart:
#     #                 PendingSettingChange.objects.create(
#     #                     setting_key=key,
#     #                     new_value=value,
#     #                     requested_by=request.user
#     #                 )
                    
#     #                 return Response({
#     #                     "message": f"Setting '{key}' updated successfully",
#     #                     "requires_restart": True,
#     #                     "instruction": "This change will take effect after restarting the application."
#     #                 })
#     #             else:
#     #                 # Update cache if needed
#     #                 if hasattr(SettingsService, '_cache') and key in SettingsService._cache:
#     #                     SettingsService._cache[key] = {
#     #                         'value': value,
#     #                         'expires': time.time() + SettingsService._cache_timeout
#     #                     }
                        
#     #                 return Response({
#     #                     "message": f"Setting '{key}' updated successfully",
#     #                     "requires_restart": False
#     #                 })
                    
#     #     except SystemSettings.DoesNotExist:
#     #         return Response({"error": f"Setting '{key}' not found"}, status=404)
#     #     except Exception as e:
#     #         return Response({"error": f"Error updating setting: {str(e)}"}, status=500)

#     def put(self, request):
#         settings_data = request.data.get('settings')
        
#         if not settings_data or not isinstance(settings_data, list):
#             return Response({"error": "Request must include 'settings' as a list of objects"}, status=400)
        
#         results = {
#             "updated": [],
#             "not_found": [],
#             "errors": [],
#             "requires_restart": False
#         }
        
#         for setting_item in settings_data:
#             key = setting_item.get('key')
#             value = setting_item.get('value')
            
#             if not key:
#                 results["errors"].append({"error": "Setting key is required", "item": setting_item})
#                 continue
                
#             if value is None:
#                 results["errors"].append({"error": "Setting value is required", "key": key})
#                 continue
            
#             try:
#                 setting = SystemSettings.objects.get(key=key)
                
#                 # Update the value
#                 setting.value = value
#                 setting.updated_by = request.user
#                 setting.save()
                
#                 # If setting requires restart, create a pending change record
#                 if setting.requires_restart:
#                     PendingSettingChange.objects.create(
#                         setting_key=key,
#                         new_value=value,
#                         requested_by=request.user
#                     )
#                     results["requires_restart"] = True
                    
#                 # Update cache if needed
#                 if hasattr(SettingsService, '_cache') and key in SettingsService._cache:
#                     SettingsService._cache[key] = {
#                         'value': value,
#                         'expires': time.time() + SettingsService._cache_timeout
#                     }
                    
#                 results["updated"].append({
#                     "key": key, 
#                     "requires_restart": setting.requires_restart
#                 })
                    
#             except SystemSettings.DoesNotExist:
#                 results["not_found"].append(key)
#             except Exception as e:
#                 results["errors"].append({"key": key, "error": str(e)})
        
#         # Add instructions if restart is required
#         if results["requires_restart"]:
#             results["instruction"] = "Some changes will take effect after restarting the application."
            
#         # Determine appropriate status code
#         if not results["updated"]:
#             if results["not_found"] and not results["errors"]:
#                 return Response(results, status=404)  # Not found
#             elif results["errors"]:
#                 return Response(results, status=400)  # Bad request
        
#         return Response(results)
        
#     def delete(self, request):
#         key = request.data.get('key')
        
#         if not key:
#             return Response({"error": "Setting key is required"}, status=400)
        
#         try:
#             setting = SystemSettings.objects.get(key=key)
            
#             # Check if this is a critical setting that shouldn't be deleted
#             if setting.key in ["LINE_CHANNEL_SECRET", "LINE_ACCESS_TOKEN", "CHATBOT_MASCOT_NAME"]:
#                 return Response({
#                     "error": f"Cannot delete critical setting '{key}'",
#                     "message": "This setting is required for application functionality."
#                 }, status=403)
            
#             # Store the key for response
#             deleted_key = setting.key
            
#             # Delete the setting
#             setting.delete()
            
#             # Also clear from cache if it exists
#             if hasattr(SettingsService, '_cache') and deleted_key in SettingsService._cache:
#                 del SettingsService._cache[deleted_key]
            
#             return Response({
#                 "message": f"Setting '{deleted_key}' deleted successfully"
#             })
            
#         except SystemSettings.DoesNotExist:
#             return Response({"error": f"Setting '{key}' not found"}, status=404)
#         except Exception as e:
#             return Response({"error": f"Error deleting setting: {str(e)}"}, status=500)

# # Version 01
# class SettingsImageUploadView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAdminUser]
    
#     def post(self, request):
#         key = request.data.get('key')
#         image_file = request.FILES.get('image')
        
#         if not key or not image_file:
#             return Response({"error": "Both key and image file are required"}, status=400)
            
#         azure_storage = AzureBlobStorage()
#         blob_name = f"settings/{key}/{image_file.name}"
        
#         image_url = azure_storage.upload_file(image_file, blob_name)
        
#         result = SettingsService.update_setting(
#             key=key, 
#             value=image_url,
#             user=request.user
#         )
        
#         # Update the value_type
#         setting = SystemSettings.objects.get(key=key)
#         setting.value_type = 'image'
#         setting.save()
        
#         return Response({
#             **result,
#             "url": image_url
#         })

# # Version 02
# class SettingsImageUploadView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsSupervisorOrHigher]
    
#     def post(self, request):
#         key = request.data.get('key')
#         image_file = request.FILES.get('image_file')

#         filename = image_file.name.lower()
#         allowed_extensions = ['.jpg', '.jpeg', '.png']
#         is_valid_extension = any(filename.endswith(ext) for ext in allowed_extensions)

#         # Validate uploaded image_file
#         if not key or not image_file:
#             return Response({"error": "Both key and image file are required"}, status=400)
#         if not is_valid_extension:
#             return Response({
#                 'error': 'Invalid file type. Allowed types are: JPEG, PNG, PDF',
#                 'filename': image_file.name
#             }, status=status.HTTP_400_BAD_REQUEST)
        
#         # Upload image_file
#         azure_storage = AzureBlobStorage()
#         blob_name = f"settings/{key}/{image_file.name}"
#         image_url = azure_storage.upload_file(image_file, blob_name)

#         # Create or Update SettingsService instance
#         result = SettingsService.update_setting(
#             key=key, 
#             value=image_url,
#             user=request.user
#         )

#         # Update the value_type
#         setting = SystemSettings.objects.get(key=key)
#         setting.value_type = 'image'
#         setting.save()
        
#         return Response({
#             **result,
#             "url": image_url
#         })


# # Version 03 - If image, add SAS token to system instance's values
# class SettingsImageUploadView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsSupervisorOrHigher]
    
#     def post(self, request):
#         key = request.data.get('key')
#         image_file = request.FILES.get('image_file')

#         filename = image_file.name.lower()
#         allowed_extensions = ['.jpg', '.jpeg', '.png']
#         is_valid_extension = any(filename.endswith(ext) for ext in allowed_extensions)

#         # Validate uploaded image_file
#         if not key or not image_file:
#             return Response({"error": "Both key and image file are required"}, status=400)
#         if not is_valid_extension:
#             return Response({
#                 'error': 'Invalid file type. Allowed types are: JPEG, PNG, PDF',
#                 'filename': image_file.name
#             }, status=status.HTTP_400_BAD_REQUEST)
        
#         # Upload image_file
#         azure_storage = AzureBlobStorage()
#         blob_name = f"settings/{key}/{image_file.name}"
#         blob_client = azure_storage.container_client.get_blob_client(blob_name)
#         image_url = azure_storage.upload_file(image_file, blob_name)

#         # Generate SAS token for this uploaded file
#         start_time = datetime.utcnow()
#         # expiry_time = start_time + timedelta(days=1) # SAS's lifetime
#         expiry_time = start_time + timedelta(days=365) # SAS's lifetime
#             # azure_storage.container_client,
#             # azure_storage.blob_service_client
#         sas_token = generate_blob_sas(
#             # account_name=self.blob_service_client.account_name,
#             account_name=azure_storage.blob_service_client.account_name,
#             # container_name=self.container_client.container_name,
#             container_name=azure_storage.container_client.container_name,
#             blob_name=blob_name,
#             # account_key=self.blob_service_client.credential.account_key,
#             account_key=azure_storage.blob_service_client.credential.account_key,
#             permission=BlobSasPermissions(read=True),
#             start=start_time,
#             expiry=expiry_time,
#             protocol="https"  # Force HTTPS
#         )
#         # Build the full URL
#         sas_url = f"{blob_client.url}?{sas_token}"

#         # TODO - Delete this or Log this
#         logger.info(f"get_file_url_image_v2's blob_client.url : {blob_client.url}")
#         logger.info(f"get_file_url_image_v2's sas_url : {sas_url}")
#         print(f"{bcolors.HEADER}|----get_file_url_image_v2's blob_client.url : {blob_client.url}{bcolors.ENDC}")
#         print(f"{bcolors.HEADER}|----get_file_url_image_v2's sas_url : {sas_url}{bcolors.ENDC}")
            

#         # Create or Update SettingsService instance
#         result = SettingsService.update_setting(
#             key=key, 
#             value=sas_url,
#             user=request.user
#         )

#         # Update the value_type
#         setting = SystemSettings.objects.get(key=key)
#         setting.value_type = 'image'
#         setting.save()
        
#         return Response({
#             **result,
#             "url": sas_url
#         })

class RestartServiceView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsSupervisorOrHigher]
    
    def post(self, request):
        # Check if automated restarts are allowed
        if not getattr(django_settings, 'ALLOW_AUTOMATED_RESTARTS', False):
            return Response({
                "message": "Restart required but automated restarts are disabled",
                "instructions": "Please contact system administrator to restart the service"
            })
            
        try:
            # Execute restart based on deployment type
            deployment_type = getattr(django_settings, 'DEPLOYMENT_TYPE', None)
            
            if deployment_type == 'docker':
                # Signal for docker restart
                open('/tmp/restart_required', 'w').close()
            elif deployment_type == 'systemd':
                # Use systemctl restart
                service_name = getattr(django_settings, 'SERVICE_NAME', 'devproject')
                subprocess.run(['systemctl', 'restart', service_name])
            else:
                return Response({
                    "message": "Unknown deployment type, manual restart required",
                    "deployment_type": deployment_type
                })
            
            return Response({
                "message": "Service restart initiated",
                "status": "pending"
            })
        except Exception as e:
            return Response({
                "error": f"Failed to restart service: {str(e)}"
            }, status=500)
        
# Moved to setting/_views/setting_business_hours_crud.py
# class BusinessHoursView(APIView):
#     """
#     API view for managing company business hours
#     """
#     permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
    
#     def get(self, request):
#         """Get the company business hours"""
#         business_hours = SchedulingService.get_business_hours()
#         serializer = BusinessHoursSerializer(business_hours)
#         return Response(serializer.data)
    
#     def post(self, request):
#         """Update the company business hours"""
#         serializer = BusinessHoursSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
        
#         try:
#             updated_hours = SchedulingService.update_business_hours(
#                 serializer.validated_data, 
#                 request.user
#             )
#             return Response(updated_hours, status=status.HTTP_200_OK)
#         except ValueError as e:
#             return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class UserScheduleViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user work schedules (admin view)
    """
    queryset = UserSchedule.objects.all()
    serializer_class = UserScheduleSerializer
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]


class MyScheduleView(APIView):
    """
    API view for users to manage their own schedule
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get the current user's schedule"""
        user_schedule = SchedulingService.get_user_schedule(request.user)
        return Response(user_schedule)
    
    def post(self, request):
        """Update the current user's schedule"""
        serializer = ScheduleDataSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            SchedulingService.update_user_schedule(
                request.user,
                serializer.validated_data
            )
            # Get and return the updated schedule
            updated_schedule = SchedulingService.get_user_schedule(request.user)
            return Response(updated_schedule, status=status.HTTP_200_OK)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AvailableUsersView(APIView):
    """
    API view to find available users
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get list of currently available users"""
        # Optional filter by department or other criteria could be added here
        users = User.objects.filter(is_active=True)
        
        available_users = SchedulingService.find_available_users(users)
        
        # Format the response to include key user details
        user_data = [{
            'id': user.id,
            'username': user.username,
            'name': getattr(user, 'name', user.get_full_name()),
            'email': user.email,
        } for user in available_users]
        
        return Response({
            'count': len(user_data),
            'users': user_data
        })
    
# class MessageTemplateView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated, IsSupervisorOrHigher]

#     def get(self, request):
#         templates = MessageTemplate.objects.all()
#         data = [
#             {
#                 "id": template.id,
#                 "sentence": template.sentence,
#                 "label": template.label,
#                 "parent": template.parent.id if template.parent else None,
#                 "message_type": template.message_type
#             }
#             for template in templates
#         ]
#         return Response(data)

#     def post(self, request):
#         data = request.data
#         try:
#             template = MessageTemplate.objects.create(
#                 sentence=data.get("sentence"),
#                 label=data.get("label"),
#                 parent=data.get("parent", None),
#                 message_type_text=data.get("message_type", {}).get("text"),
#                 message_type_quick_reply=data.get("message_type", {}).get("quick_reply", []),
#                 message_type_image_map=data.get("message_type", {}).get("image_map", {}),
#                 message_type_image_carousel=data.get("message_type", {}).get("image_carousel", {}),
#                 message_type_carousel=data.get("message_type", {}).get("carousel", {}),
#                 message_type_confirm_template=data.get("message_type", {}).get("confirm_template", {}),
#                 message_type_buttons_template=data.get("message_type", {}).get("buttons_template", {})
#             )
#             return Response({"id": template.id}, status=status.HTTP_200_OK)
#         except Exception as e:
#             return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

#     def put(self, request):
#         pass

#     def delete(self, request):
#        pass

class MessageTemplateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]

    def get(self, request):
        templates = MessageTemplate.objects.all()
        data = [
            {
                "id": template.id,
                "sentence": template.sentence,  # Now returns a list
                "sentence_count": template.sentence_count,
                "label": template.label,
                "parent": template.parent if template.parent else None,
                "status": template.status,
                "department": template.department,
                "message_type": template.message_type
            }
            for template in templates
        ]
        return Response(data)

    def post(self, request):
        data = request.data
        try:
            # Validate sentence is a list
            sentence_data = data.get("sentence", [])
            if not isinstance(sentence_data, list):
                return Response(
                    {"error": "Sentence must be a list of strings"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            template = MessageTemplate.objects.create(
                sentence=sentence_data,  # Store as list
                label=data.get("label"),
                parent=data.get("parent", None),
                message_type_text=data.get("message_type", {}).get("text"),
                message_type_quick_reply=data.get("message_type", {}).get("quick_reply", []),
                message_type_image_map=data.get("message_type", {}).get("image_map", {}),
                message_type_image_carousel=data.get("message_type", {}).get("image_carousel", {}),
                message_type_carousel=data.get("message_type", {}).get("carousel", {}),
                message_type_confirm_template=data.get("message_type", {}).get("confirm_template", {}),
                message_type_buttons_template=data.get("message_type", {}).get("buttons_template", {}),
                status = data.get("status", None),
                department = data.get("department", []),
                created_by=request.user,
                updated_by=request.user
            )
            
            serializer = MessageTemplateSerializer(template)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        template_id = request.data.get("id")
        if not template_id:
            return Response(
                {"error": "Template ID is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            template = MessageTemplate.objects.get(id=template_id)
            
            # Update sentence if provided
            if "sentence" in request.data:
                sentence_data = request.data["sentence"]
                if not isinstance(sentence_data, list):
                    return Response(
                        {"error": "Sentence must be a list of strings"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                template.sentence = sentence_data
            
            # Update other fields if provided
            if "label" in request.data:
                template.label = request.data["label"]
            if "parent" in request.data:
                template.parent = request.data["parent"]

            if "status" in request.data:
                template.status = request.data["status"]
            if "department" in request.data:
                department_data = request.data["department"]
                if not isinstance(department_data, list):
                    return Response(
                        {"error": "Department must be a list"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                template.department = department_data
            
            # Update message types if provided
            if "message_type" in request.data:
                msg_type = request.data["message_type"]
                if "text" in msg_type:
                    template.message_type_text = msg_type["text"]
                if "quick_reply" in msg_type:
                    template.message_type_quick_reply = msg_type["quick_reply"]
                if "image_map" in msg_type:
                    template.message_type_image_map = msg_type["image_map"]
                if "image_carousel" in msg_type:
                    template.message_type_image_carousel = msg_type["image_carousel"]
                if "carousel" in msg_type:
                    template.message_type_carousel = msg_type["carousel"]
                if "confirm_template" in msg_type:
                    template.message_type_confirm_template = msg_type["confirm_template"]
                if "buttons_template" in msg_type:
                    template.message_type_buttons_template = msg_type["buttons_template"]
            
            template.updated_by = request.user
            template.save()
            
            serializer = MessageTemplateSerializer(template)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except MessageTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request):
        template_id = request.data.get("id")
        if not template_id:
            return Response(
                {"error": "Template ID is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            template = MessageTemplate.objects.get(id=template_id)
            template.delete()
            return Response(
                {"message": "Template deleted successfully"}, 
                status=status.HTTP_204_NO_CONTENT
            )
        except MessageTemplate.DoesNotExist:
            return Response(
                {"error": "Template not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )