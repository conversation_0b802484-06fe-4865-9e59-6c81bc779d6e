<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { subscriptionStatus } from '$lib/stores/subscriptionStatus';
    import { SubscriptionService } from '$lib/api/features/subscription/subscription.service';
    import type { SubscriptionStatusResponse } from '$lib/api/types/subscription-api';
    import type { LayoutData } from './$types';

    export let data: LayoutData;  // Get data from layout

    const subscriptionService = new SubscriptionService();

    // Loading and error states
    let isLoading = true;
    let errorMessage = '';
    let hasCheckedSubscription = false;

    // Configuration
    const API_TIMEOUT = 10000; // 10 seconds
    const MAX_RETRIES = 1;

    // Debug logging function
    function logError(context: string, error: any, additionalInfo?: any) {
        const timestamp = new Date().toISOString();
        const errorDetails = {
            timestamp,
            context,
            error: error?.message || error,
            stack: error?.stack,
            additionalInfo,
            userData: {
                userId: data.id,
                email: data.email,
                role: data.role
            }
        };
        console.error(`[Subscription Check Error] ${context}:`, errorDetails);
    }

    // Create a timeout promise
    function createTimeoutPromise(ms: number) {
        return new Promise((_, reject) => {
            setTimeout(() => reject(new Error(`Request timeout after ${ms}ms`)), ms);
        });
    }

    // Retry wrapper for API calls
    async function withRetry<T>(
        operation: () => Promise<T>,
        retries: number = MAX_RETRIES,
        context: string = 'API call'
    ): Promise<T> {
        let lastError: any;

        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                if (attempt > 0) {
                    logError(`${context} - Retry attempt`, { attempt, maxRetries: retries });
                    // Add a small delay before retry
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                }

                return await operation();
            } catch (error) {
                lastError = error;
                logError(`${context} - Attempt ${attempt + 1} failed`, error);

                if (attempt === retries) {
                    throw lastError;
                }
            }
        }

        throw lastError;
    }

    // Main subscription checking function
    async function checkSubscriptionStatus() {
        if (hasCheckedSubscription) return;

        try {
            isLoading = true;
            errorMessage = '';
            hasCheckedSubscription = true;

            // Validate that we have an access token
            const accessToken = data.access_token;
            if (!accessToken) {
                // This should not happen since layout server checks for token
                // But if it does, it means there's a serious auth issue
                logError('Missing access token', {
                    message: 'Access token missing despite passing layout server checks',
                    layoutData: data
                });
                throw new Error('Authentication token not available');
            }

            // Make API call with timeout and retry logic
            const response = await withRetry<SubscriptionStatusResponse>(async () => {
                const apiCall = subscriptionService.getSubscriptionStatus(accessToken);
                const timeoutPromise = createTimeoutPromise(API_TIMEOUT);

                return await Promise.race([apiCall, timeoutPromise]) as SubscriptionStatusResponse;
            }, MAX_RETRIES, 'Subscription status check');

            // Handle successful response
            if (response.res_status === 200 && response.data) {
                subscriptionStatus.setStatus(
                    response.data.is_active,
                    response.data.expires_at
                );

                // Navigate based on subscription status
                if (response.data.is_active) {
                    await goto('/chat_center');
                } else {
                    await goto('/subscription');
                }
            } else {
                // Handle API error responses
                const errorMsg = response.error_msg || 'Failed to fetch subscription status';
                logError('API returned error response', {
                    status: response.res_status,
                    message: response.message,
                    error_msg: response.error_msg,
                    data: response.data
                });

                subscriptionStatus.setError(errorMsg);
                throw new Error(errorMsg);
            }

        } catch (error) {
            // Handle all errors by redirecting to login
            logError('Subscription check failed completely', error, {
                hasCheckedSubscription,
                accessToken: data.access_token ? 'present' : 'missing'
            });

            // Set error in store
            subscriptionStatus.setError(
                error instanceof Error ? error.message : 'Network error while checking subscription status'
            );

            // Show error message briefly before redirect
            errorMessage = 'Unable to verify subscription status. Redirecting to login...';

            // Redirect to login after a brief delay to show the error message
            setTimeout(async () => {
                try {
                    await goto('/login');
                } catch (redirectError) {
                    logError('Failed to redirect to login', redirectError);
                    // Fallback: force page reload to login
                    window.location.href = '/login';
                }
            }, 2000);

        } finally {
            isLoading = false;
        }
    }

    // Initialize subscription checking on mount
    onMount(() => {
        checkSubscriptionStatus();
    });
</script>

<svelte:head>
    <title>Loading...</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8 p-8">
        <div class="text-center">
            {#if isLoading}
                <!-- Loading State -->
                <div class="space-y-4">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <h2 class="text-xl font-semibold text-gray-900">Verifying Subscription</h2>
                    <p class="text-gray-600">Please wait while we check your subscription status...</p>
                </div>
            {:else if errorMessage}
                <!-- Error State -->
                <div class="space-y-4">
                    <div class="rounded-full h-12 w-12 bg-red-100 flex items-center justify-center mx-auto">
                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-red-900">Subscription Check Failed</h2>
                    <p class="text-red-700">{errorMessage}</p>
                    <div class="animate-pulse">
                        <div class="h-2 bg-red-200 rounded-full w-3/4 mx-auto"></div>
                    </div>
                </div>
            {:else}
                <!-- Fallback State (should not be visible) -->
                <div class="space-y-4">
                    <div class="rounded-full h-12 w-12 bg-gray-100 flex items-center justify-center mx-auto">
                        <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900">Processing...</h2>
                    <p class="text-gray-600">Please wait...</p>
                </div>
            {/if}
        </div>
    </div>
</div>
