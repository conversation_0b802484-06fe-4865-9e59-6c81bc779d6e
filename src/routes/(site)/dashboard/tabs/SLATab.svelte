<script>
  import { onMount } from 'svelte';
  import SLAConfiguration from './SLAConfiguration.svelte';
  import PerformanceData from './PerformanceData.svelte';
  import {slaConfigurationService} from '$lib/api/features/sla/sla_configuration.service';
  import { t } from '$lib/stores/i18n';

  // SLA Configuration
  export let access_token;
  let showSLAConfig = false;
  let slaData = [];

  function openSLAConfig() {
    showSLAConfig = true;
  }

  // function closeSLAConfig() {
  //   showSLAConfig = false;
  // }

  async function fetchSlaData() {
    try {
      const data = await slaConfigurationService.getAll(access_token);
      // console.log('--fetchSlaData--', data.slas.data)
      slaData = data?.slas.data || [];
      
    } catch (err) {
      console.error('Failed to fetch SLA data:', err);
      slaData = []; // fallback empty array
    }
  }

  // Save SLA configuration
  async function saveSlaConfig() {
    try {
      const response = await fetch('/api/sla', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          slaTargets: slaData
        })
      });

      if (response.ok) {
        console.log('SLA configuration saved successfully');
        // Refresh data after save
        await fetchSlaData();
        // Trigger performance data refresh
        if (performanceDataComponent) {
          performanceDataComponent.refreshData();
        }
      } else {
        console.error('Failed to save SLA configuration');
      }
    } catch (err) {
      console.error('Error saving SLA configuration:', err);
    }
  }

  // Reference to performance data component
  let performanceDataComponent;

  onMount(() => {
    fetchSlaData();
  });

  // Remove the export since we're now using the component internally
  // export { slaData };
</script>

<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
  <div class="flex justify-between items-start">
    <!-- Title -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">{t('sla_title')}</h1>
      <p class="text-gray-600">{t('sla_description')}</p>
    </div>
    <!-- Button -->
    <button 
      on:click={openSLAConfig}
      class="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
    >
      {t('configure_sla')}
    </button>
  </div>
  
  <!-- SLA Summary -->
  <!-- {#if slaData.length > 0}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
      {#each slaData.slice(0, 6) as sla}
        <div class="p-3 bg-gray-50 rounded-lg">
          <div class="text-sm font-medium text-gray-700">
            {t(sla.sla.toLowerCase())} - {t(sla.channel.toLowerCase())}
          </div>
          <div class="text-lg font-semibold text-gray-900">
            {sla.value}{t(sla.unit) === 'percentage' ? '%' : t(sla.unit) === 'score' ? '' : ` ${t(sla.unit)}`}
          </div>
        </div>
      {/each}
    </div>
  {/if} -->
</div>

{#if showSLAConfig}
  <SLAConfiguration 
    bind:show={showSLAConfig} 
    bind:slaData
    on:save={saveSlaConfig}
  />
{/if}

<!-- Performance Data Component -->
<!-- <PerformanceData bind:this={performanceDataComponent} slaData={slaData} /> -->

<style>
  :global(.overflow-y-auto::-webkit-scrollbar) {
    width: 8px;
  }
  
  :global(.overflow-y-auto::-webkit-scrollbar-track) {
    background: #f1f5f9;
    border-radius: 4px;
  }
  
  :global(.overflow-y-auto::-webkit-scrollbar-thumb) {
    background: #cbd5e1;
    border-radius: 4px;
  }
  
  :global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
    background: #94a3b8;
  }
</style>