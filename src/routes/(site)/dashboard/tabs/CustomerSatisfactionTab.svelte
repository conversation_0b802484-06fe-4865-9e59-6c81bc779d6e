<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { onMount } from 'svelte';
    import { currentLanguage } from '$lib/stores/languagePreference';
    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';

    export let sla_configuration = [];
    
    // Import service and types
    import { dashboardService } from '$lib/api/features/dashboard/dashboard.service';
    import type {
        AgentPerformanceParams,
        CSATTicket
    } from '$lib/api/types/dashboard';

    // Define props for startDate, endDate and selectedAgent
    export let startDate: string | undefined;
    export let endDate: string | undefined;
    export let selectedAgent: string = 'All Agents';
    export let agentNames: string[] = [];

    // Data fetched from backend
    let csatTickets: CSATTicket[] = [];

    // Loading and error states
    let isLoadingCSATTickets: boolean = true;

    // State for expanded table modal
    let isCSATTicketsExpanded: boolean = false;

    // Sorting state and functionality
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        csatTickets: { column: 'csatScore', direction: 'desc' }
    };

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // Set initial sort direction based on key
            if (key === 'totalUsedTime' || key === 'csatScore') {
                newDirection = 'desc';
            } else {
                newDirection = 'asc';
            }
        }

        currentSort = {
            ...currentSort,
            [tableName]: { column: String(key), direction: newDirection }
        };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            // Custom sort for 'priority' field
            if (key === 'priority') {
                const priorityOrder = { 'Immediately': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
                const aP = priorityOrder[aValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                const bP = priorityOrder[bValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                return newDirection === 'asc' ? aP - bP : bP - aP;
            }

            // Custom sort for 'sentiment' field
            if (key === 'sentiment') {
                const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                return newDirection === 'asc' ? aS - bS : bS - aS;
            }

            // Sort for date-time strings
            if (['createdDateTime', 'closedDateTime'].includes(String(key))) {
                const valA = new Date(aValue as string).getTime();
                const valB = new Date(bValue as string).getTime();
                return newDirection === 'asc' ? valA - valB : valB - valA;
            } 
            
            // General sort for numbers
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            } 
            
            // General sort for strings
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            }

            // Return 0 if types are incompatible or unknown
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;

                // Custom sort for 'priority' field
                if (key === 'priority') {
                    const priorityOrder = { 'Immediately': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
                    const aP = priorityOrder[aValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                    const bP = priorityOrder[bValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                    return direction === 'asc' ? aP - bP : bP - aP;
                }

                // Custom sort for 'sentiment' field
                if (key === 'sentiment') {
                    const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                    const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                    const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                    return direction === 'asc' ? aS - bS : bS - aS;
                }

                // Sort for date-time strings
                if (['createdDateTime', 'closedDateTime'].includes(String(key))) {
                    const valA = new Date(aValue as string).getTime();
                    const valB = new Date(bValue as string).getTime();
                    return direction === 'asc' ? valA - valB : valB - valA;
                } 
                
                // General sort for numbers
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                } 
                
                // General sort for strings
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }

                // Return 0 if types are incompatible or unknown
                return 0;
            });
        }
        return dataArray;
    }

    const lang = $currentLanguage

    // Add initialization tracking
    let hasInitialized = false;
    let isInitializing = true;

    // Track previous values to detect actual changes
    let previousAgent: string | undefined = undefined;
    let previousStartDate: string | undefined = undefined;
    let previousEndDate: string | undefined = undefined;
    let previousLanguage: string | undefined = undefined;

    async function fetchData() {
        console.log("fetchData called for CustomerSatisfactionTab");
        console.log(`Current filter settings (from props): selectedAgent='${selectedAgent}', startDate='${startDate}', endDate='${endDate}'`);

        // Reset loading state
        isLoadingCSATTickets = true;

        try {
            const params: AgentPerformanceParams = {
                startDate,
                endDate,
                selectedAgent,
                agentNames
            };

            const data = await dashboardService.fetchCSATData(params, applyInitialSort);

            // Update component state with fetched data
            agentNames = data.agentNames;
            csatTickets = data.csatTickets;

        } catch (error) {
            console.error('Error fetching CSAT data:', error);
            // Reset data on error
            agentNames = [];
            csatTickets = [];
        } finally {
            // Reset loading state
            isLoadingCSATTickets = false;
        }
    }

    // Excel download function using service
    async function handleDownloadCSATTicketsExcel() {
        const params: AgentPerformanceParams = { startDate, endDate, selectedAgent, agentNames };
        await dashboardService.downloadCSATTicketsExcel(params, (key: string) => t(key));
    }
        
    onMount(() => {
        console.log("Component mounted: initiating fetchData");
        isInitializing = true;

        // Set initial values to track changes
        previousAgent = selectedAgent;
        previousStartDate = startDate;
        previousEndDate = endDate;
        previousLanguage = $currentLanguage;
        
        fetchData().finally(() => {
            hasInitialized = true;
            isInitializing = false;
        });
    });

    // Only trigger fetchData after initialization is complete and values actually change
    $: if (hasInitialized && !isInitializing) {
        const currentLanguage = $currentLanguage;
        
        // Check if any values have actually changed
        const hasAgentChanged = selectedAgent !== previousAgent;
        const hasStartDateChanged = startDate !== previousStartDate;
        const hasEndDateChanged = endDate !== previousEndDate;
        const hasLanguageChanged = currentLanguage !== previousLanguage;
        
        if (hasAgentChanged || hasStartDateChanged || hasEndDateChanged || hasLanguageChanged) {
            console.log("Filter values changed, triggering fetchData:", {
                agent: { old: previousAgent, new: selectedAgent },
                startDate: { old: previousStartDate, new: startDate },
                endDate: { old: previousEndDate, new: endDate },
                language: { old: previousLanguage, new: currentLanguage }
            });
            
            // Update tracking variables
            previousAgent = selectedAgent;
            previousStartDate = startDate;
            previousEndDate = endDate;
            previousLanguage = currentLanguage;
            
            fetchData();
        }
    }

    import {getPriorityColor, getSentimentColor, getStatusColor, formatOverdueTime, formatDateTimeForDisplay} from "$lib/utils"
</script>

{#if isCSATTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbCSAT.lowCSATbyTicket')}</h3>
                <button on:click={() => isCSATTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingCSATTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if csatTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'ticketNo', 'csatTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.csatTickets.column === 'ticketNo'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'ticketStatus', 'csatTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.csatTickets.column === 'ticketStatus'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'customerName', 'csatTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.csatTickets.column === 'customerName'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'priority', 'csatTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.csatTickets.column === 'priority'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'sentiment', 'csatTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.csatTickets.column === 'sentiment'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'agentName', 'csatTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.csatTickets.column === 'agentName'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'totalUsedTime', 'csatTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.csatTickets.column === 'totalUsedTime'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>                                
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'createdDateTime', 'csatTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.csatTickets.column === 'createdDateTime'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'closedDateTime', 'csatTickets')}>
                                    {t('dbChatPerformance.closedTime')}
                                    {#if currentSort.csatTickets.column === 'closedDateTime'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'csatScore', 'csatTickets')}>
                                    {t('dbCSAT.csatScore')}
                                    {#if currentSort.csatTickets.column === 'csatScore'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each csatTickets as item (item.id)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                            {t("tickets_" + item.ticketStatus)}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                            {t("tickets_priority_" + item.priority.toLowerCase())}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment ? item.sentiment : "-"}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime, lang)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime, lang)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 font-semibold">{item.csatScore.toFixed(1)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbCSAT.lowCSATbyTicket')}</h2>
            <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                <button 
                    on:click={() => isCSATTicketsExpanded = true}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <ExpandOutline class="h-5 w-5" />
                </button>
                <button
                    on:click={handleDownloadCSATTicketsExcel}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <DownloadOutline class="h-5 w-5" />
                </button>
            </div>
        </div>

        <div class="overflow-y-auto max-h-96">
            {#if isLoadingCSATTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if csatTickets.length === 0}
                <div class="text-gray-600 text-center text-lg py-0">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'ticketNo', 'csatTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.csatTickets.column === 'ticketNo'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'ticketStatus', 'csatTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.csatTickets.column === 'ticketStatus'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'customerName', 'csatTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.csatTickets.column === 'customerName'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'priority', 'csatTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.csatTickets.column === 'priority'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'sentiment', 'csatTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.csatTickets.column === 'sentiment'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'agentName', 'csatTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.csatTickets.column === 'agentName'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'totalUsedTime', 'csatTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.csatTickets.column === 'totalUsedTime'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>                            
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'createdDateTime', 'csatTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.csatTickets.column === 'createdDateTime'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => csatTickets = sortTable(csatTickets, 'closedDateTime', 'csatTickets')}>
                                {t('dbChatPerformance.closedTime')}
                                {#if currentSort.csatTickets.column === 'closedDateTime'}
                                    {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => csatTickets = sortTable(csatTickets, 'csatScore', 'csatTickets')}>
                                    {t('dbCSAT.csatScore')}
                                    {#if currentSort.csatTickets.column === 'csatScore'}
                                        {#if currentSort.csatTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each csatTickets as item (item.id)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                        {t("tickets_" + item.ticketStatus)}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {t("tickets_priority_" + item.priority.toLowerCase())}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment ? item.sentiment : "-"}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime, lang)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime, lang)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 font-semibold">{item.csatScore.toFixed(1)}</td>
                            </tr>
                        {/each}

                    </tbody>
                </table>
            {/if}
        </div>
    </div>
</div>

        