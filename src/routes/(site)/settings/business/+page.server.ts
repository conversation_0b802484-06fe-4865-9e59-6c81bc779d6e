// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import { services } from "$lib/api/features";
import { redirect, error, fail } from '@sveltejs/kit';
import type { PageServerLoad } from "../$types";
import type { Actions } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token')
    let refresh_token = cookies.get('refresh_token');

    if (!access_token) {
        return {
            system_setting: [],
            user_info: {},
            connectors : {
                line :  [],
                facebook : [],
                whatsapp : [],
            },
            error: 'No access token available'
        };
    }

    const response_userInfo = await services.users.getUserInfo(access_token);
    if (response_userInfo.res_status === 401) {
        throw error(401, 'Invalid access token!!!');
    }

    // console.log('response_userInfo role :', response_userInfo.users)
    // Checking if the user is supervisor or admin in order to see the settings page
    const role = response_userInfo.users.roles[0].name;

    if (role !== 'Admin') {
        throw redirect(302, '/');
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response_system_setting = await services.system_setting.getAll(access_token);
            const response_line_connectors = await services.connector.getAllLineConnectors(access_token);
            // const response_line_liff_apps = await services.connector.getAllLineLiffApps(access_token);
            
            if (response_system_setting.res_status === 401 || response_line_connectors.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            // console.log(`SETTING PAGE LOAD`)
            // console.log(response_system_setting.system_setting)
            // console.log(response_line_connectors.line_connectors)

            return {
                system_setting: response_system_setting.system_setting || [],
                user_info: response_userInfo.users || {},
                connectors : {
                    line : response_line_connectors.line_connectors || [],
                    // liff_apps: response_line_liff_apps.liff_apps || [],
                    facebook : [],
                    whatsapp : [],
                },
                access_token: access_token,
            }

        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');

            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
};

export const actions: Actions = {
    update_system_setting: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Parse the settings from form data
        const settingsJson = formData.get('settings');
        const settings = settingsJson ? JSON.parse(settingsJson.toString()) : [];

        const url = `${getBackendUrl()}/setting/api/settings/`;

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ "settings": settings })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Status: ${errorData.message || 'Unknown error'} (${response.status})`);
            }

            return { success: true };
        } catch (error) {
            console.error('Update Settings error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },
    // ==================== Upload Actions ====================
    upload_image: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        
        // Get the image file and key from formData
        const imageFile = formData.get('image_file');
        const key = formData.get('key')?.toString();
        
        // Basic validation
        if (!imageFile || !(imageFile instanceof File)) {
            return fail(400, { error: 'Image file is required.' });
        }
        
        if (!key) {
            return fail(400, { error: 'Key is required.' });
        }
        
        try {
            const response = await services.system_setting.uploadImage(key, imageFile, access_token);

            return { 
                success: true, 
                message: 'Upload Image successfully',
            };
            
        } catch (error) {
            console.error('Image upload error:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while uploading the image' });
        }
    },

    // ==================== LINE Connector Actions ====================
    create_line_connector: async ({ request, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');

        // Get LINE connector data from formData
        const name = formData.get('name')?.toString() ?? '';
        const channel_id = formData.get('channel_id')?.toString() ?? '';
        const channel_secret = formData.get('channel_secret')?.toString() ?? '';
        const channel_access_token = formData.get('channel_access_token')?.toString() ?? '';
        const line_provider_id = formData.get('line_provider_id')?.toString() ?? '';
        const line_provider_name = formData.get('line_provider_name')?.toString() ?? '';

        // Basic validation
        if (!name || !channel_id || !channel_secret || !channel_access_token || !line_provider_id || !line_provider_name ) {
            return fail(400, { error: 'All fields are required.' });
        }

        const connectorData = {
            name,
            channel_id,
            channel_secret,
            channel_access_token,
            line_provider_id,
            line_provider_name
        };

        // console.log('connectorData', connectorData)

        try {
            const response = await services.connector.createLineConnector(access_token, connectorData);

            // console.log('response', response)

            if (response.res_status !== 200 && response.res_status !== 201) {
                throw new Error(`Failed to create LINE connector. Status: ${response.res_status}. ${response.error_msg || ''}`);
            }

            return { success: true, message: 'LINE connector created successfully', data: response.schedules };

        } catch (error) {
            console.error('Error creating LINE connector:', error);
            return fail(500, { error: error instanceof Error ? error.message : 'An error occurred while creating the LINE connector' });
        }
    },


    // Update LINE connector
    update_line_connector: async ({ request, locals }) => {
        const data = await request.formData();
        
        try {
            const connectorId = data.get('connector_id') as string;
            const updateData = {
                name: data.get('name') as string,
                channel_id: data.get('channel_id') as string,
                channel_secret: data.get('channel_secret') as string,
                channel_access_token: data.get('channel_access_token') as string,
                line_provider_id: data.get('line_provider_id') as string,
            };

            // Validate required fields
            if (!connectorId || !updateData.name || !updateData.channel_id || 
                !updateData.channel_secret || !updateData.channel_access_token || !updateData.line_provider_id) {
                return fail(400, { error: 'All fields are required' });
            }

            const token = locals.session?.access_token || locals.user?.access_token;
            if (!token) {
                return fail(401, { error: 'Unauthorized' });
            }

            const response = await services.connector.updateLineConnectorById(token, connectorId, updateData);

            if (response.res_status === 200) {
                return { success: true, data: response.schedules };
            } else {
                return fail(response.res_status || 400, { 
                    error: response.error_msg || 'Failed to update LINE connector' 
                });
            }

        } catch (error) {
            console.error('Error in update_line_connector action:', error);
            return fail(500, { error: 'Internal server error' });
        }
    },

    // Toggle LINE connector status (enable/disable)
    toggle_line_status: async ({ request, locals }) => {
        const data = await request.formData();
        
        try {
            const connectorId = data.get('connector_id') as string;
            const action = data.get('action') as string;
            const reason = data.get('reason') as string || '';

            // Validate required fields
            if (!connectorId || !action || !['enable', 'disable'].includes(action)) {
                return fail(400, { error: 'Invalid request parameters' });
            }

            const token = locals.session?.access_token || locals.user?.access_token;
            if (!token) {
                return fail(401, { error: 'Unauthorized' });
            }

            let response;
            if (action === 'enable') {
                response = await services.connector.enableLineChannel(token, connectorId, reason);
            } else {
                response = await services.connector.disableLineChannel(token, connectorId, reason);
            }

            if (response.res_status === 200) {
                return { success: true, action: action, data: response.schedules };
            } else {
                return fail(response.res_status || 400, { 
                    error: response.error_msg || `Failed to ${action} LINE connector` 
                });
            }

        } catch (error) {
            console.error('Error in toggle_line_status action:', error);
            return fail(500, { error: 'Internal server error' });
        }
    },

    // Test LINE connector
    test_line_connector: async ({ request, locals }) => {
        const data = await request.formData();
        
        try {
            const connectorId = data.get('connector_id') as string;

            // Validate required fields
            if (!connectorId) {
                return fail(400, { error: 'Connector ID is required' });
            }

            const token = locals.session?.access_token || locals.user?.access_token;
            if (!token) {
                return fail(401, { error: 'Unauthorized' });
            }

            const response = await services.connector.verifyLineConnector(token, connectorId);

            if (response.res_status === 200) {
                return { success: true, data: response.schedules };
            } else {
                return fail(response.res_status || 400, { 
                    error: response.error_msg || 'Failed to test LINE connector' 
                });
            }

        } catch (error) {
            console.error('Error in test_line_connector action:', error);
            return fail(500, { error: 'Internal server error' });
        }
    },

    // Delete LINE connector (optional)
    delete_line_connector: async ({ request, locals }) => {
        const data = await request.formData();
        
        try {
            const connectorId = data.get('connector_id') as string;

            // Validate required fields
            if (!connectorId) {
                return fail(400, { error: 'Connector ID is required' });
            }

            const token = locals.session?.access_token || locals.user?.access_token;
            if (!token) {
                return fail(401, { error: 'Unauthorized' });
            }

            const response = await services.connector.deleteLineConnector(token, connectorId);

            if (response.res_status === 200 || response.res_status === 204) {
                return { success: true, data: response.schedules };
            } else {
                return fail(response.res_status || 400, { 
                    error: response.error_msg || 'Failed to delete LINE connector' 
                });
            }

        } catch (error) {
            console.error('Error in delete_line_connector action:', error);
            return fail(500, { error: 'Internal server error' });
        }
    },

    add_liff_app: async ({ request, locals, cookies }) => {
		try {
			const formData = await request.formData();
			
            const access_token = cookies.get('access_token');

			// Extract form data
            const channel_id        = formData.get('channel_id') as string;
            const channel_name      = formData.get('channel_name') as string;
            const provider_id       = formData.get('provider_id') as string;
			const line_liff_app_name= formData.get('line_liff_app_name') as string;
			const line_liff_id      = formData.get('line_liff_id') as string;
			const line_liff_url     = formData.get('line_liff_url') as string;

			const endpoint = formData.get('endpoint') as string;

			const line_login_channel_id = formData.get('line_login_channel_id') as string;
			const line_login_channel_name = formData.get('line_login_channel_name') as string;
			const line_login_channel_secret = formData.get('line_login_channel_secret') as string;

			// Validate required fields
			if (!line_liff_app_name || !line_liff_id || !line_liff_url || 
				!line_login_channel_id || !line_login_channel_name || !line_login_channel_secret) {
				return fail(400, {
					error: 'All required fields must be filled'
				});
			}

			// Create new LIFF app object
			const newLiffApp = {
				line_liff_app_name,
				line_liff_id,
				line_liff_url,
				endpoint,
				purpose: endpoint.includes('/pdpa/') ? 'PDPA' : 'CUSTOM' // Auto-detect purpose
			};

			const connectorData = {
				channel: {
					channel_id: channel_id,  
					channel_name: channel_name, 
					provider_id: provider_id 
				},
				login: {
					channel_id: line_login_channel_id,
					channel_name: line_login_channel_name,
					channel_secret: line_login_channel_secret
				},
				liff_apps: [newLiffApp]
			};

			console.log('Sending connector data:', JSON.stringify(connectorData, null, 2));

            const response = await services.connector.createConnectLINEChannelLINELoginLineLIFFApp(access_token,connectorData);
			console.log('API Response:', response);

			return {
				success: true,
				data: response.liff_app
			};

		} catch (error) {
			console.error('Error in add_liff_app action:', error);
			return fail(500, {
				error: error instanceof Error ? error.message : 'Internal server error'
			});
		}
	},

    get_line_login_n_line_liff_apps_by_line_channel: async ({ request, locals, cookies }) => {
        try {
            const formData = await request.formData();
            const access_token = cookies.get('access_token');

            const linedata = {
                channel_id: formData.get('channel_id'),
                provider_id: formData.get('provider_id')
            };

            // Call the API to get LINE login and LIFF apps
            const response = await services.connector.getLineLoginNLiffAppByLineChannelId(access_token, linedata);
            // console.log('Response from API:', response.data);
            // console.log('Response liff_apps from API:', response.data.liff_apps);
            if (response.res_status === 200) {
                return { 
                    success: true, 
                    data: response.data
                };
            } else {
                return fail(response.res_status || 400, { error: response.error_msg || 'Failed to fetch LINE login and LIFF apps' });
            }
        }
        catch (error) {
            console.error('Error in get_line_login_n_line_liff_apps_by_line_channel action:', error);
            return fail(500, { error: 'Internal server error' });
        }
    }
};